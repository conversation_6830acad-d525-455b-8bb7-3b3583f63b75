#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据获取程序
用于测试和获取万象会员数据API的数据
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_fetcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class APIDataFetcher:
    """API数据获取器"""

    def __init__(self, base_url: str, cookies: str = None, request_interval: int = 10):
        """
        初始化API数据获取器

        Args:
            base_url: API基础URL
            cookies: Cookie字符串
            request_interval: 请求间隔时间（秒）
        """
        self.base_url = base_url
        self.request_interval = request_interval
        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'Sec-Ch-Ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Connection': 'keep-alive'
        })

        # 设置Cookie
        if cookies:
            self.session.headers['Cookie'] = cookies
            logger.info("已设置Cookie信息")
    
    def test_api_connection(self) -> bool:
        """
        测试API连接是否正常

        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info("正在测试API连接...")

            # 构建测试URL（获取第1页数据）
            test_params = {
                'time_type': '0',
                'type': 'grow',
                'page': '1',
                'size': '100',
                'mch_id': '0',
                'json': '1'
            }

            response = self.session.get(self.base_url, params=test_params, timeout=30)

            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容长度: {len(response.text)}")
            logger.info(f"响应内容前200字符: {response.text[:200]}")

            response.raise_for_status()

            # 尝试解析JSON
            data = response.json()

            logger.info(f"API连接测试成功！状态码: {response.status_code}")
            logger.info(f"返回数据类型: {type(data)}")

            # 打印部分数据结构用于验证
            if isinstance(data, dict):
                logger.info(f"返回数据键: {list(data.keys())}")
            elif isinstance(data, list):
                logger.info(f"返回数据长度: {len(data)}")
                if data:
                    logger.info(f"第一个元素类型: {type(data[0])}")

            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"API连接失败 - 网络错误: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"API连接失败 - JSON解析错误: {e}")
            logger.error(f"响应内容: {response.text[:500] if 'response' in locals() else 'No response'}")
            return False
        except Exception as e:
            logger.error(f"API连接失败 - 未知错误: {e}")
            return False
    
    def fetch_page_data(self, page: int) -> Optional[Dict[str, Any]]:
        """
        获取指定页面的数据
        
        Args:
            page: 页码
            
        Returns:
            Dict: 页面数据，如果失败返回None
        """
        try:
            logger.info(f"正在获取第 {page} 页数据...")
            
            params = {
                'time_type': '0',
                'type': 'grow',
                'page': str(page),
                'size': '100',
                'mch_id': '0',
                'json': '1'
            }
            
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            logger.info(f"第 {page} 页数据获取成功")
            return {
                'page': page,
                'timestamp': datetime.now().isoformat(),
                'data': data
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取第 {page} 页数据失败 - 网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"获取第 {page} 页数据失败 - JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"获取第 {page} 页数据失败 - 未知错误: {e}")
            return None
    
    def fetch_all_pages(self, start_page: int = 1, end_page: int = 5) -> List[Dict[str, Any]]:
        """
        获取所有页面的数据
        
        Args:
            start_page: 起始页码
            end_page: 结束页码
            
        Returns:
            List: 所有页面的数据列表
        """
        all_data = []
        
        for page in range(start_page, end_page + 1):
            # 获取页面数据
            page_data = self.fetch_page_data(page)
            
            if page_data:
                all_data.append(page_data)
            else:
                logger.warning(f"第 {page} 页数据获取失败，跳过")
            
            # 如果不是最后一页，等待指定时间
            if page < end_page:
                logger.info(f"等待 {self.request_interval} 秒后获取下一页...")
                time.sleep(self.request_interval)
        
        return all_data
    
    def save_merged_data(self, all_data: List[Dict[str, Any]], output_file: str = 'merged_data.json'):
        """
        保存合并后的数据到JSON文件
        
        Args:
            all_data: 所有页面的数据
            output_file: 输出文件名
        """
        try:
            # 创建合并后的数据结构
            merged_data = {
                'fetch_info': {
                    'total_pages': len(all_data),
                    'fetch_time': datetime.now().isoformat(),
                    'api_url': self.base_url
                },
                'pages': all_data
            }
            
            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已成功保存到 {output_file}")
            logger.info(f"总共获取了 {len(all_data)} 页数据")
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")


def main():
    """主函数"""
    # API URL
    api_url = "https://a48360.tmwanba.com/finance-reprot/mchuser-detail"

    # Cookie信息 - 请在这里填入您的Cookie
    # 从浏览器开发者工具中复制Cookie字符串
    cookies = "backend=h4aa5i9l93mjs1ihbem2sbqgqh; _csrf-backend=7e5ceb33f165ec5b3ee34a8f5280193e62dea2b561db10dcc3de69013d8abd0ca%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%2235R_2uGCxnLE3w6kl_1T29iOaUNUlhLk%22%3B%7D"

    # 如果没有Cookie，提示用户
    if not cookies or cookies.strip() == "":
        logger.error("请在代码中设置Cookie信息！")
        logger.error("请从浏览器开发者工具中复制Cookie字符串并填入main()函数中的cookies变量")
        return

    # 创建数据获取器
    fetcher = APIDataFetcher(api_url, cookies=cookies, request_interval=10)

    # 测试API连接
    if not fetcher.test_api_connection():
        logger.error("API连接测试失败，程序退出")
        logger.error("请检查Cookie是否正确或是否已过期")
        return

    logger.info("API连接测试成功，开始获取数据...")

    # 获取1-5页的数据
    all_data = fetcher.fetch_all_pages(start_page=1, end_page=5)

    if not all_data:
        logger.error("没有获取到任何数据")
        return

    # 保存合并后的数据
    output_filename = f"merged_api_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    fetcher.save_merged_data(all_data, output_filename)

    logger.info("程序执行完成！")


if __name__ == "__main__":
    main()
